<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.SES.mapper.UserMapper">

    <select id="pageQueny" resultType="com.SES.entity.User">
        select * from user
        <where>
            <if test = "username != null and username != ''">
                and username like concat('%',#{username},'%')
            </if>
        </where>
        order by createtime desc
    </select>

    <update id="update" parameterType="User">
        update user
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createTime != null">createtime = #{updateTime},</if>
            <if test="createUser != null">create_user = #{updateUser},</if>
            <if test="updateTime != null">updatetime = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
        </set>
        where id = #{id}
    </update>
</mapper>
